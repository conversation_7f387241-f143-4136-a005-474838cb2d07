var express = require('express');
var router = express.Router();

// 路由级中间件 - 只对 router 生效
router.use((req, res, next) => {
  console.log('📌 路由级中间件触发');
  next();
});

router.use((req, res, next) => {
  console.log('📌 路由级中间件触发1');
  next();
}, (req, res, next) => {
  console.log('📌 路由级中间件触发2');
  next();
});

/* GET users listing. */
router.get('/', function(req, res, next) {
  res.send('respond with a resource');
});

module.exports = router;
