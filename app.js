var express = require('express');
var path = require('path');
var cookieParser = require('cookie-parser');
var logger = require('morgan');
const db = require('./db/models'); // 引入 Sequelize 模型

var indexRouter = require('./routes/index');
var usersRouter = require('./routes/users');
const { cache } = require('react');

var app = express();

// 下面是：不做任何限定的中间件
app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

// 下面是：应用层使用路由的中间件
app.use('/users', function (req, res, next) {
    console.log('限定请求路径的中间件单函数:', req.method);
    next();
});

// 多函数形式
app.use(
    "/users",
    (req, res, next) => {
        console.log("限定请求路径的中间件多函数1", req.method);
        //这个next 会脱离当前处理栈，往后查找匹配调用
        next();
    },
    (req, res, next) => {
        console.log("限定请求路径的中间件多函数2", req.method);
        next();
    }
);
app.use('/', indexRouter);
app.use('/users', usersRouter);

app.get('/other', (req, res) => {
  db.sequelize.authenticate()
    .then(async () => {
      console.log('数据库连接成功');
      const dbres = await db.User.findAll(null, {
        cache: true, // 启用缓存
      });
      console.log('获取到的 user表数据 :>> ', dbres);
      res.send(dbres);
    })
    .catch(err => {
      console.error('数据库连接失败:', err);
      res.status(500).send('数据库连接失败');
    });
    
});

// 正常路由
app.get('/error', (req, res, next) => {
  next(new Error('Something went wrong!'));
});

// 错误处理中间件（注意有 4 个参数）可全局使用
app.use('/error', (err, req, res, next) => {
  console.error('❗错误处理中间件6666：', err.message);
  res.status(500).send('服务器内部错误666');
  next('route')
});



// 错误处理中间件（注意有 4 个参数）可全局使用
app.use((err, req, res, next) => {
  console.error('❗错误处理中间件：', err.message);
  res.status(500).send('服务器内部错误');
});


module.exports = app;


// TODO: 使用 http-errors 中间件来处理状态码