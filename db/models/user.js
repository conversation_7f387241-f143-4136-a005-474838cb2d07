'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class User extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }

    /**
     * 创建新用户
     * @param {Object} userData - 用户数据
     * @returns {Promise<User>} 创建的用户实例
     */
    static async createUser(userData) {
      try {
        const user = await this.create(userData);
        return user;
      } catch (error) {
        throw new Error(`创建用户失败: ${error.message}`);
      }
    }

    /**
     * 根据ID获取用户
     * @param {number} id - 用户ID
     * @returns {Promise<User|null>} 用户实例或null
     */
    static async getUserById(id) {
      try {
        const user = await this.findByPk(id);
        return user;
      } catch (error) {
        throw new Error(`获取用户失败: ${error.message}`);
      }
    }

    /**
     * 根据邮箱获取用户
     * @param {string} email - 用户邮箱
     * @returns {Promise<User|null>} 用户实例或null
     */
    static async getUserByEmail(email) {
      try {
        const user = await this.findOne({ where: { email } });
        return user;
      } catch (error) {
        throw new Error(`根据邮箱获取用户失败: ${error.message}`);
      }
    }

    /**
     * 获取所有用户（支持分页）
     * @param {Object} options - 查询选项
     * @param {number} options.page - 页码（从1开始）
     * @param {number} options.limit - 每页数量
     * @param {Object} options.where - 查询条件
     * @returns {Promise<{users: User[], total: number, totalPages: number}>}
     */
    static async getAllUsers(options = {}) {
      try {
        const { page = 1, limit = 10, where = {} } = options;
        const offset = (page - 1) * limit;

        const { count, rows } = await this.findAndCountAll({
          where,
          limit: parseInt(limit),
          offset: parseInt(offset),
          order: [['createdAt', 'DESC']]
        });

        return {
          users: rows,
          total: count,
          totalPages: Math.ceil(count / limit),
          currentPage: parseInt(page)
        };
      } catch (error) {
        throw new Error(`获取用户列表失败: ${error.message}`);
      }
    }

    /**
     * 更新用户信息
     * @param {number} id - 用户ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<User|null>} 更新后的用户实例或null
     */
    static async updateUser(id, updateData) {
      try {
        const user = await this.findByPk(id);
        if (!user) {
          return null;
        }

        await user.update(updateData);
        return user;
      } catch (error) {
        throw new Error(`更新用户失败: ${error.message}`);
      }
    }

    /**
     * 删除用户
     * @param {number} id - 用户ID
     * @returns {Promise<boolean>} 是否删除成功
     */
    static async deleteUser(id) {
      try {
        const user = await this.findByPk(id);
        if (!user) {
          return false;
        }

        await user.destroy();
        return true;
      } catch (error) {
        throw new Error(`删除用户失败: ${error.message}`);
      }
    }

    /**
     * 批量删除用户
     * @param {number[]} ids - 用户ID数组
     * @returns {Promise<number>} 删除的用户数量
     */
    static async deleteUsers(ids) {
      try {
        const deletedCount = await this.destroy({
          where: {
            id: ids
          }
        });
        return deletedCount;
      } catch (error) {
        throw new Error(`批量删除用户失败: ${error.message}`);
      }
    }

    /**
     * 搜索用户
     * @param {string} keyword - 搜索关键词
     * @param {Object} options - 查询选项
     * @returns {Promise<User[]>} 匹配的用户列表
     */
    static async searchUsers(keyword, options = {}) {
      try {
        const { page = 1, limit = 10 } = options;
        const offset = (page - 1) * limit;
        const { Op } = require('sequelize');

        const users = await this.findAll({
          where: {
            [Op.or]: [
              { firstName: { [Op.like]: `%${keyword}%` } },
              { lastName: { [Op.like]: `%${keyword}%` } },
              { email: { [Op.like]: `%${keyword}%` } }
            ]
          },
          limit: parseInt(limit),
          offset: parseInt(offset),
          order: [['createdAt', 'DESC']]
        });

        return users;
      } catch (error) {
        throw new Error(`搜索用户失败: ${error.message}`);
      }
    }
  }

  User.init({
    firstName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: '名字不能为空'
        },
        len: {
          args: [1, 50],
          msg: '名字长度必须在1-50个字符之间'
        }
      },
      comment: '用户的名字'
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: '姓氏不能为空'
        },
        len: {
          args: [1, 50],
          msg: '姓氏长度必须在1-50个字符之间'
        }
      },
      comment: '用户的姓氏'
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        msg: '邮箱已存在'
      },
      validate: {
        isEmail: {
          msg: '请输入有效的邮箱地址'
        },
        notEmpty: {
          msg: '邮箱不能为空'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'User',
    timestamps: true, // 启用时间戳
    paranoid: true,   // 启用软删除
  });
  return User;
};